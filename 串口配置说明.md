# 双串口配置说明

## 概述

已成功配置双串口系统：
- **USART1**：专用于电机控制通信
- **USART2**：专用于printf调试输出

这样可以避免调试信息和电机控制命令的冲突，提高系统稳定性。

## 硬件连接

### USART1（电机控制专用）
- **TX引脚**：PA9 (USART1_TX)
- **RX引脚**：PA10 (USART1_RX)
- **波特率**：9600 bps
- **用途**：与电机驱动器通信

**连接方式：**
```
STM32F1        电机驱动器
PA9 (TX)  -->  RX
PA10 (RX) <--  TX
GND       ---  GND
```

### USART2（调试输出专用）
- **TX引脚**：PA2 (USART2_TX)
- **RX引脚**：PA3 (USART2_RX)
- **波特率**：115200 bps
- **用途**：printf调试输出

**连接方式：**
```
STM32F1        USB转串口模块/调试器
PA2 (TX)  -->  RX
PA3 (RX)  <--  TX
GND       ---  GND
```

### USART3（相机数据接收）
- **TX引脚**：PB10 (USART3_TX)
- **RX引脚**：PB11 (USART3_RX)
- **波特率**：115200 bps
- **用途**：接收相机坐标数据

## 软件配置

### 初始化函数

```c
// 电机控制串口初始化
void usart1_motor_init(void);

// 调试输出串口初始化
void usart2_debug_init(void);

// 统一初始化函数（调用上述两个函数）
void usart_init(void);
```

### 使用方法

#### 1. 电机控制（USART1）
```c
// 电机控制命令通过USART1发送
Emm_V5_Pos_Control(M1_addr, 0, 200, 20, 1000, false, false);

// 底层发送函数
usart_SendCmd(cmd, len);  // 自动使用USART1
usart_SendByte(data);     // 自动使用USART1
```

#### 2. 调试输出（USART2）
```c
// printf自动使用USART2输出
printf("电机移动到位置: (%d, %d)\n", x, y);
printf("当前步数: X=%d, Y=%d\n", Usartdata.x_step, Usartdata.y_step);
```

#### 3. 相机数据接收（USART3）
```c
// 相机数据通过USART3接收（已有实现）
// 在USART3_IRQHandler中处理接收数据
```

## 中断优先级配置

```c
// USART1（电机控制）- 最高优先级
NVIC_IRQChannelPreemptionPriority = 0;
NVIC_IRQChannelSubPriority = 0;

// USART2（调试输出）- 中等优先级
NVIC_IRQChannelPreemptionPriority = 2;
NVIC_IRQChannelSubPriority = 2;

// USART3（相机数据）- 较低优先级
NVIC_IRQChannelPreemptionPriority = 3;
NVIC_IRQChannelSubPriority = 3;
```

## 使用示例

### 完整的初始化和使用流程

```c
int main(void)
{
    // 系统初始化
    board_init();  // 自动调用usart_init()
    
    // 现在可以同时使用两个串口
    printf("系统初始化完成\n");  // 通过USART2输出
    
    // 电机控制通过USART1
    vInterpolation_Point_Serial(1000, 500);
    
    printf("电机移动完成\n");  // 通过USART2输出
    
    while(1) {
        // 主循环
        vData_Get();  // 处理USART3相机数据
        
        if(Usartdata.x != 0 || Usartdata.y != 0) {
            printf("接收到坐标: (%d, %d)\n", Usartdata.x, Usartdata.y);  // USART2
            vInterpolation_Point_Serial(Usartdata.x, Usartdata.y);       // USART1
            Usartdata.x = 0;
            Usartdata.y = 0;
        }
        
        Delayms(10);
    }
}
```

## 调试工具配置

### 串口调试助手设置

**电机控制监控（USART1）：**
- 波特率：9600
- 数据位：8
- 停止位：1
- 校验位：无
- 连接到PA9/PA10

**调试信息查看（USART2）：**
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验位：无
- 连接到PA2/PA3

### Keil调试配置

如果使用Keil的调试功能，建议：
1. 使用USART2查看printf输出
2. 使用逻辑分析仪监控USART1的电机控制命令
3. 使用示波器查看电机控制时序

## 注意事项

1. **引脚冲突**：确保PA2/PA3没有被其他功能占用
2. **波特率匹配**：
   - 电机驱动器必须设置为9600波特率
   - 调试终端必须设置为115200波特率
3. **中断处理**：USART1中断主要用于电机反馈，USART2一般不需要中断
4. **缓冲区管理**：如果需要接收大量调试数据，可以为USART2添加接收缓冲区

## 故障排除

### 常见问题

1. **printf无输出**：
   - 检查PA2连接
   - 确认调试终端波特率为115200
   - 检查USART2初始化是否正确

2. **电机控制失效**：
   - 检查PA9连接到电机驱动器RX
   - 确认电机驱动器波特率为9600
   - 检查USART1初始化是否正确

3. **数据冲突**：
   - 确认没有在USART1上发送调试信息
   - 确认没有在USART2上发送电机控制命令

### 测试方法

```c
// 测试USART2调试输出
void test_debug_output(void)
{
    printf("USART2 调试输出测试\n");
    printf("当前系统时间: %d ms\n", HAL_GetTick());
}

// 测试USART1电机控制
void test_motor_control(void)
{
    printf("开始测试电机控制\n");  // USART2输出
    vInterpolation_Point_Serial(100, 100);  // USART1控制
    printf("电机控制测试完成\n");  // USART2输出
}
```

现在你的系统已经配置为双串口模式，可以同时进行电机控制和调试输出而不会相互干扰！
