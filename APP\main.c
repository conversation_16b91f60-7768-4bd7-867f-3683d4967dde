#include "board.h"
#include "stdio.h"
#include "motor.h"
#include "Emm_V5.h"

/*
* 主函数：
* 实现X轴和Y轴电机交替转动（串口控制模式）
* 同时支持串口3接收相机数据并优先处理
*/

int main(void)
{
	// 系统初始化
	board_init();

	// 初始化步进计数器
	Usartdata.x_step = 0;
	Usartdata.y_step = 0;

	printf("System ready, Serial Motor Control Mode\r\n");
	printf("X-axis and Y-axis alternating rotation started...\r\n");
	vLED_Blink(2);  // LED闪烁2次表示系统就绪

	// 交替转动参数配置
	uint16_t steps_per_cycle = 200;   // 每次转动的步数
	uint16_t motor_speed = 300;       // 转动速度(RPM)
	uint8_t acceleration = 30;        // 加速度
	uint16_t cycle_delay = 1000;      // 轴间切换延时(毫秒)

	printf("Starting alternating motor rotation:\r\n");
	printf("- Steps per cycle: %d\r\n", steps_per_cycle);
	printf("- Motor speed: %d RPM\r\n", motor_speed);
	printf("- Acceleration: %d\r\n", acceleration);
	printf("- X-axis address: %d, Y-axis address: %d\r\n", M1_addr, M0_addr);

	// 主循环计数器
	static uint32_t loop_counter = 0;
	static uint32_t alternating_interval = 100000;  // 交替转动间隔

	for(;;)
	{
		// 检查是否有新的串口3数据并解析
		vData_Get();  // 这个函数会自动检查、解析并打印接收到的数据

		// 如果接收到相机数据，优先处理
		if(Usartdata.x != 0 || Usartdata.y != 0) {
			printf("Camera data received: (%d, %d), executing movement...\r\n",
			       Usartdata.x, Usartdata.y);
			vInterpolation_Point_Serial(Usartdata.x, Usartdata.y);
			Usartdata.x = 0;
			Usartdata.y = 0;
			continue;  // 处理完相机数据后继续循环
		}

		// 交替转动控制
		loop_counter++;
		if(loop_counter >= alternating_interval)
		{
			loop_counter = 0;

			// 执行一次交替转动
			vAlternating_Motor_Control(steps_per_cycle, motor_speed, acceleration, cycle_delay);

			// LED闪烁指示运动完成
			vLED_Blink(1);
		}
	}
}
