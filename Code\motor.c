/*
 * motor.c - 步进电机控制模块
 * 功能：控制双轴云台运动，支持插补算法和PID控制
 */

#include "motor.h"
#include "stdio.h"
#include "board.h"
#include "math.h"
#include "ReturnToZero.h"

/* ======================== 宏定义常量 ======================== */
// 坐标转换相关参数
#define ImageToActual (1.93f)    // 像素到实际距离转换系数 (mm/pixel)
#define Distance (1000.0f)       // 摄像头到屏幕距离 (mm)
#define Step_Sub (207*64)        // 步进电机细分参数
#define Alpha (0.995f)           // 坐标转换补偿系数

// 数学工具宏
#define _Round(input) ((int16_t)((input)>0.0f?(input)+0.5f:(input)-0.5f))
#define _Sign(input) ((input)!=0?((input)>0?1:-1):0)
#define _Abs(input) ((input)<0?-(input):(input))

/* ======================== 全局变量定义 ======================== */
// PID控制器实例
PID_Struct_ motor1_PID = {{0,},0,0,0,};  // X轴电机PID控制器
PID_Struct_ motor2_PID = {{0,},0,0,0,};  // Y轴电机PID控制器

// 运动控制相关变量
uint8_t ReturnToZero_Flag = 0;           // 回原点标志位
int16_t actual_point[30][2] = {{0,},};   // 转换后的实际坐标数组

/* ======================== 辅助工具函数 ======================== */

/**
 * @brief  延时函数
 * @param  ms: 延时毫秒数
 * @retval None
 */
void Delayms(u16 ms)
{
	u16 i,j;
	u8 k;
	for(i=0;i<ms;i++)
		for(j=0;j<0x0500;j++) k++;
}

/* ======================== 硬件初始化函数 ======================== */

/**
 * @brief  电机定时器初始化 (TIM2)
 * @param  None
 * @retval None
 * @note   配置TIM2用于电机控制定时，频率1kHz
 */
void Motor_Timer_init(void)
{
	// NVIC中断配置
	NVIC_InitTypeDef NVIC_Structure;
	NVIC_Structure.NVIC_IRQChannel = TIM2_IRQn;
	NVIC_Structure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Structure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_Structure.NVIC_IRQChannelSubPriority = 3;
	NVIC_Init(&NVIC_Structure);

	// TIM2时钟使能和配置
	RCC->APB1ENR|=1<<0;          // 使能TIM2时钟
	TIM2->PSC=72-1;              // 预分频设置：72MHz/72=1MHz
	TIM2->ARR=1000-1;            // 自动重装载：1MHz/1000=1kHz
	TIM2->DIER |=1<<0;           // 使能更新中断
	TIM2->CR1|=1<<0;             // 启动定时器
}

/**
 * @brief  PWM定时器中断初始化 (TIM3, TIM4)
 * @param  None
 * @retval None
 * @note   配置TIM3和TIM4的更新中断，用于步进电机控制
 */
void Pwm_UIE_init(void)
{
	// 使能TIM3和TIM4更新中断
	TIM3->DIER|=1<<0;
	TIM4->DIER|=1<<0;

	// TIM3中断配置
	NVIC_InitTypeDef NVIC_Struct;
	NVIC_Struct.NVIC_IRQChannel = TIM3_IRQn;
	NVIC_Struct.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Struct.NVIC_IRQChannelPreemptionPriority = 4;
	NVIC_Struct.NVIC_IRQChannelSubPriority = 4;
	NVIC_Init(&NVIC_Struct);

	// TIM4中断配置
	NVIC_Struct.NVIC_IRQChannel = TIM4_IRQn;
	NVIC_Struct.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Struct.NVIC_IRQChannelPreemptionPriority = 4;
	NVIC_Struct.NVIC_IRQChannelSubPriority = 4;
	NVIC_Init(&NVIC_Struct);
}

// 控制模式选择宏定义
#define MOTOR_CONTROL_MODE_PULSE  0   // 脉冲控制模式
#define MOTOR_CONTROL_MODE_SERIAL 1   // 串口控制模式

// 当前控制模式（可以通过修改这个值来切换模式）
#define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_SERIAL

/**
 * @brief  步进电机驱动初始化
 * @param  None
 * @retval None
 * @note   根据CURRENT_MOTOR_CONTROL_MODE选择初始化模式
 */
void vDriverInit(void)
{
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
	// 脉冲控制模式初始化
	GPIO_InitTypeDef GPIO_Structure;

	// 使能GPIOB时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);

	// 配置GPIO为推挽输出模式
	GPIO_Structure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_Structure.GPIO_Speed = GPIO_Speed_50MHz;

	// X轴电机步进和方向引脚
	GPIO_Structure.GPIO_Pin = GPIO_Pin_5;//Motor1_Step
	GPIO_Init(GPIOB,&GPIO_Structure);
	GPIO_Structure.GPIO_Pin = GPIO_Pin_6;//Motor1_Dir
	GPIO_Init(GPIOB ,&GPIO_Structure);
	GPIO_SetBits(GPIOB,GPIO_Pin_6);//默认正转

	// Y轴电机步进和方向引脚
	GPIO_Structure.GPIO_Pin = GPIO_Pin_7;//Motor2_Step
	GPIO_Init(GPIOB,&GPIO_Structure);
	GPIO_Structure.GPIO_Pin = GPIO_Pin_8;//Motor2_Dir
	GPIO_Init(GPIOB ,&GPIO_Structure);
	GPIO_SetBits(GPIOB,GPIO_Pin_8);//默认正转

	// 初始化步进计数器
	Usartdata.x_step=0;
	Usartdata.y_step=0;
	printf("Pulse Motor Control init OK\n");

#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
	// 串口控制模式初始化
	vMotor_Serial_Init();

#endif
}

/* ======================== 电机控制函数 ======================== */

#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
/**
 * @brief  X轴电机单步运动（脉冲控制版本）
 * @param  None
 * @retval None
 * @note   根据方向位状态更新步进计数器
 */
void vOne_Step_X(void)
{
	// 根据方向位更新步进计数器
	if(GPIOB->ODR&(1<<6))        // 正转
		Usartdata.x_step++;
	else                         // 反转
		Usartdata.x_step--;

	// 产生步进脉冲
	GPIOB->ODR|=(1<<5);          // 脉冲高电平
	Delayms(1);                  // 脉冲宽度
	GPIOB->ODR&=~(1<<5);         // 脉冲低电平
}

/**
 * @brief  Y轴电机单步运动（脉冲控制版本）
 * @param  None
 * @retval None
 * @note   根据方向位状态更新步进计数器
 */
void vOne_Step_Y(void)
{
	// 根据方向位更新步进计数器
	if(GPIOB->ODR&(1<<8))        // 正转
		Usartdata.y_step++;
	else                         // 反转
		Usartdata.y_step--;

	// 产生步进脉冲
	GPIOB->ODR|=(1<<7);          // 脉冲高电平
	Delayms(1);                  // 脉冲宽度
	GPIOB->ODR&=~(1<<7);         // 脉冲低电平
}

#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
/**
 * @brief  X轴电机单步运动（串口控制版本）
 * @param  dir: 方向控制，0为正转，1为反转
 * @retval None
 * @note   使用串口发送单步位置控制命令
 */
void vOne_Step_X(uint8_t dir)
{
	// 使用串口发送单步位置控制命令
	// 参数：地址，方向，速度(RPM)，加速度，脉冲数，相对运动，不启用同步
	Emm_V5_Pos_Control(M1_addr, dir, 100, 10, 1, false, false);

	// 更新步进计数器
	if(dir == 0)        // 正转
		Usartdata.x_step++;
	else                // 反转
		Usartdata.x_step--;
}

/**
 * @brief  Y轴电机单步运动（串口控制版本）
 * @param  dir: 方向控制，0为正转，1为反转
 * @retval None
 * @note   使用串口发送单步位置控制命令
 */
void vOne_Step_Y(uint8_t dir)
{
	// 使用串口发送单步位置控制命令
	// 参数：地址，方向，速度(RPM)，加速度，脉冲数，相对运动，不启用同步
	Emm_V5_Pos_Control(M0_addr, dir, 100, 10, 1, false, false);

	// 更新步进计数器
	if(dir == 0)        // 正转
		Usartdata.y_step++;
	else                // 反转
		Usartdata.y_step--;
}
#endif

/* ======================== PID控制算法 ======================== */

/**
 * @brief  PID控制算法
 * @param  _PID_: PID控制器结构体指针
 * @param  error: 当前误差值
 * @retval 控制输出值
 * @note   增量式PID算法
 */
int16_t PID_Control(PID_Struct_ *_PID_,int16_t error)
{
	int16_t Out;

	// 误差更新
	_PID_->error[0] = (float)error;

	// PID各项计算
	_PID_->pout = (_PID_->error[0]-_PID_->error[1])*_PID_->kp;  // 比例项
	_PID_->iout = _PID_->error[0]*_PID_->ki;                    // 积分项
	_PID_->dout = (_PID_->error[0]-2*_PID_->error[1]+_PID_->error[2])*_PID_->kd;  // 微分项

	// 输出计算
	_PID_->out[1] = _PID_->pout + _PID_->iout +_PID_->dout;
	_PID_->out[0] += _PID_->out[1];

	// 误差历史更新
	_PID_->error[2] = _PID_->error[1];
	_PID_->error[1] = _PID_->error[0];

	Out = (int16_t)_PID_->out[0];
	return Out;
}

/* ======================== 坐标转换与插补算法 ======================== */

/**
 * @brief  图像坐标转换为实际坐标
 * @param  Image: 图像坐标数组
 * @param  cnt: 坐标点数量
 * @retval None
 * @note   将图像处理坐标转换为云台实际步进数并执行运动
 */
void vImage_To_Actual(int16_t Image[][2],uint8_t cnt)//像素点到屏幕实际的坐标转换
{
	float Alpha_x,Alpha_y;

	// 坐标转换循环
	for(uint8_t i=0;i<cnt;i++)
	{
		// X轴坐标转换：像素->角度->步数
		Alpha_x=atan2(Image[i][0]*ImageToActual,Distance);
		actual_point[i][0]=_Round(Alpha*Alpha_x*Step_Sub);

		// Y轴坐标转换：像素->角度->步数
		Alpha_y=atan2(Image[i][1]*ImageToActual,Distance);
		actual_point[i][1]=_Round(Alpha*Alpha_y*Step_Sub);
	}

	// 执行插补运动
	vInterpolation_Move(cnt);
}


/**
 * @brief  多点插补运动控制
 * @param  cnt: 运动点数量
 * @retval None
 * @note   按顺序执行多个坐标的插补运动，根据控制模式选择插补函数
 */
void vInterpolation_Move(uint8_t cnt)
{
	for(uint8_t i=0;i<cnt;i++)
	{
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
		vInterpolation_Point(actual_point[i][0],actual_point[i][1]);
#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
		vInterpolation_Point_Serial(actual_point[i][0],actual_point[i][1]);
#endif

		// 检查回原点标志
		if(ReturnToZero_Flag)
		{
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
			vInterpolation_Point(0,0);     // 回到原点
#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
			vInterpolation_Point_Serial(0,0);     // 回到原点
#endif
			ReturnToZero_Flag=0;
			break;
		}
	}
}

/**
 * @brief  单点直线插补算法
 * @param  POINT_X: 目标X坐标
 * @param  POINT_Y: 目标Y坐标
 * @retval 0-无运动, 1-运动完成, 2-中断回原点
 * @note   使用Bresenham直线算法实现双轴同步运动
 */
uint8_t vInterpolation_Point(int16_t POINT_X,int16_t POINT_Y)
{
	static int16_t now_x=0;   // 当前X位置
	static int16_t now_y=0;   // 当前Y位置

	// 计算运动距离
	int16_t length_x;
	int16_t length_y;
	uint16_t step_cnt;
	length_x = POINT_X-now_x;
	length_y = POINT_Y-now_y;
	step_cnt = _Abs(length_x)+_Abs(length_y);
	if(!step_cnt)return 0;    // 无需运动

	// 确定运动象限 (象限判断)
	uint8_t XOY=1;
	int16_t F=0;
	if(_Sign(length_x)==-1)
	{
		if(_Sign(length_y)==-1)XOY=3;  // 第三象限 (-,-)
		else XOY=2;                    // 第二象限 (-,+)
	}
	else if(_Sign(length_y)==-1)XOY=4; // 第四象限 (+,-)
	// 第一象限 (+,+) 保持XOY=1

#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
	// 脉冲控制模式：设置GPIO方向位
	switch (XOY)//象限
	{
		case 1:GPIO_SetBits(GPIOB,GPIO_Pin_6);//++  第一象限: X正转, Y正转
		GPIO_SetBits(GPIOB,GPIO_Pin_8);break;
		case 2:GPIO_ResetBits(GPIOB,GPIO_Pin_6);//-+ 第二象限: X反转, Y正转
		GPIO_SetBits(GPIOB,GPIO_Pin_8);break;
		case 3:GPIO_ResetBits(GPIOB,GPIO_Pin_6);//-- 第三象限: X反转, Y反转
		GPIO_ResetBits(GPIOB,GPIO_Pin_8);break;
		case 4:GPIO_SetBits(GPIOB,GPIO_Pin_6);//+-  第四象限: X正转, Y反转
		GPIO_ResetBits(GPIOB,GPIO_Pin_8);break;
		default:break;
	}

	// 转换为绝对值进行插补计算
	length_x=_Abs(length_x);
	length_y=_Abs(length_y);

	// Bresenham直线插补算法
	for(uint16_t i=0;i<step_cnt;i++)
	{
		if(length_y&&length_x)    // 两轴都需要运动
		{
			if(F<0)
			{
				vOne_Step_Y();    // Y轴进步
				F+=length_x;
			}
			else
			{
				vOne_Step_X();    // X轴进步
				F-=length_y;
			}
		}
		else    // 单轴运动
		{
			if(length_x)vOne_Step_X();
			if(length_y)vOne_Step_Y();
		}

#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
	// 串口控制模式：确定方向参数
	uint8_t x_dir, y_dir;
	switch (XOY)//象限
	{
		case 1: x_dir = 0; y_dir = 0; break;  // 第一象限: X正转, Y正转
		case 2: x_dir = 1; y_dir = 0; break;  // 第二象限: X反转, Y正转
		case 3: x_dir = 1; y_dir = 1; break;  // 第三象限: X反转, Y反转
		case 4: x_dir = 0; y_dir = 1; break;  // 第四象限: X正转, Y反转
		default: x_dir = 0; y_dir = 0; break;
	}

	// 转换为绝对值进行插补计算
	length_x=_Abs(length_x);
	length_y=_Abs(length_y);

	// Bresenham直线插补算法
	for(uint16_t i=0;i<step_cnt;i++)
	{
		if(length_y&&length_x)    // 两轴都需要运动
		{
			if(F<0)
			{
				vOne_Step_Y(y_dir);    // Y轴进步
				F+=length_x;
			}
			else
			{
				vOne_Step_X(x_dir);    // X轴进步
				F-=length_y;
			}
		}
		else    // 单轴运动
		{
			if(length_x)vOne_Step_X(x_dir);
			if(length_y)vOne_Step_Y(y_dir);
		}
#endif
//	printf("%d,%d\n",Usartdata.x_step,Usartdata.y_step);

		// 检查回原点按键
		if(Key_Getlevel()==true)//return to zero
		{
			ReturnToZero_Flag=1;
			now_x=Usartdata.x_step;
			now_y=Usartdata.y_step;
			return 2;   // 中断回原点
		}
	}

	// 更新当前位置
	now_x=POINT_X;
	now_y=POINT_Y;
	return 1;   // 运动完成
}

/* ======================== 中断服务函数 ======================== */

/**
 * @brief  TIM2中断服务函数
 * @param  None
 * @retval None
 * @note   电机控制定时中断，当前为空实现
 */
void TIM2_IRQHandler(void)
{
	if(TIM2->SR&0x01)     // 检查更新中断标志
	{
		// TODO: 添加定时中断处理代码
	}
	TIM2->SR&=~(1<<0);    // 清除中断标志
}

/**
 * @brief  TIM3中断服务函数 (X轴单步模式)
 * @param  None
 * @retval None
 * @note   X轴步进电机中断处理
 */
void TIM3_IRQHandler(void)//One step mode
{
	if(TIM3->SR&0x01)//UIFt3  检查更新中断标志
	{
		TIM3->CR1&=~0x01;         // 停止定时器
		TIM3->CCR2=0;             // 清除比较值

		// 根据方向更新步进计数
		if(GPIOB->ODR&(1<<6))
			Usartdata.x_step++;
		else
			Usartdata.x_step--;
	}
	TIM3->SR&=~(1<<0);            // 清除中断标志
}

/**
 * @brief  TIM4中断服务函数 (Y轴单步模式)
 * @param  None
 * @retval None
 * @note   Y轴步进电机中断处理
 */
void TIM4_IRQHandler(void)//One step mode
{
	if(TIM4->SR&0x01)//UIFt4  检查更新中断标志
	{
		TIM4->CR1&=~0x01;         // 停止定时器
		TIM4->CCR2=0;             // 清除比较值

		// 根据方向更新步进计数
		if(GPIOB->ODR&(1<<8))
			Usartdata.y_step++;
		else
			Usartdata.y_step--;
	}
	TIM4->SR&=~(1<<0);            // 清除中断标志
}

/* ======================== 串口控制优化函数 ======================== */

/**
 * @brief  单点直线插补算法（串口控制优化版本）
 * @param  POINT_X: 目标X坐标
 * @param  POINT_Y: 目标Y坐标
 * @retval 0-无运动, 1-运动完成, 2-中断回原点
 * @note   使用多轴同步运动实现高效的双轴控制
 */
uint8_t vInterpolation_Point_Serial(int16_t POINT_X, int16_t POINT_Y)
{
	static int16_t now_x=0;   // 当前X位置
	static int16_t now_y=0;   // 当前Y位置

	// 计算运动距离
	int16_t length_x = POINT_X - now_x;
	int16_t length_y = POINT_Y - now_y;

	// 检查是否需要运动
	if(length_x == 0 && length_y == 0) return 0;

	// 确定方向
	uint8_t x_dir = (length_x >= 0) ? 0 : 1;
	uint8_t y_dir = (length_y >= 0) ? 0 : 1;

	// 计算绝对步数
	uint32_t abs_x = _Abs(length_x);
	uint32_t abs_y = _Abs(length_y);

	// 先使能电机
	Emm_V5_En_Control(M1_addr, true, false);  // X轴使能
	Emm_V5_En_Control(M0_addr, true, false);  // Y轴使能

	// 发送X轴运动命令（启用同步标志）
	if(abs_x > 0) {
		Emm_V5_Pos_Control(M1_addr, x_dir, 200, 20, abs_x, false, true);
	}

	// 发送Y轴运动命令（启用同步标志）
	if(abs_y > 0) {
		Emm_V5_Pos_Control(M0_addr, y_dir, 200, 20, abs_y, false, true);
	}

	// 触发同步运动
	Emm_V5_Synchronous_motion(All_addr);

	// 等待运动完成（简单延时，实际应用中可以通过状态查询优化）
	uint16_t max_steps = (abs_x > abs_y) ? abs_x : abs_y;
	uint16_t delay_time = max_steps * 2;  // 根据步数估算延时
	if(delay_time < 100) delay_time = 100;
	if(delay_time > 2000) delay_time = 2000;
	Delayms(delay_time);

	// 检查回原点按键
	if(Key_Getlevel()==true)//return to zero
	{
		ReturnToZero_Flag=1;
		now_x=Usartdata.x_step;
		now_y=Usartdata.y_step;
		return 2;   // 中断回原点
	}

	// 更新当前位置和步进计数器
	now_x = POINT_X;
	now_y = POINT_Y;
	Usartdata.x_step = now_x;
	Usartdata.y_step = now_y;

	return 1;   // 运动完成
}

/**
 * @brief  电机初始化（串口控制版本）
 * @param  None
 * @retval None
 * @note   初始化串口控制的电机系统
 */
void vMotor_Serial_Init(void)
{
	// 使能电机
	Emm_V5_En_Control(M1_addr, true, false);  // X轴使能
	Emm_V5_En_Control(M0_addr, true, false);  // Y轴使能

	// 设置当前位置为零点
	Emm_V5_Reset_CurPos_To_Zero(M1_addr);     // X轴位置清零
	Emm_V5_Reset_CurPos_To_Zero(M0_addr);     // Y轴位置清零

	// 初始化步进计数器
	Usartdata.x_step = 0;
	Usartdata.y_step = 0;

	printf("Serial Motor Control init OK\n");
}

/**
 * @brief  计算电机一圈所需步数
 * @param  step_angle: 步进角度（度）
 * @param  microstep: 细分数
 * @retval 一圈所需步数
 * @note   常见配置：
 *         - 1.8度步进电机，64细分：360/(1.8/64) = 12800步/圈
 *         - 1.8度步进电机，32细分：360/(1.8/32) = 6400步/圈
 *         - 0.9度步进电机，64细分：360/(0.9/64) = 25600步/圈
 */
uint16_t vCalculate_Steps_Per_Revolution(float step_angle, uint16_t microstep)
{
	return (uint16_t)(360.0f / (step_angle / microstep));
}

/**
 * @brief  获取电机参数配置
 * @param  None
 * @retval 一圈所需步数
 * @note   根据实际电机修改此函数中的参数
 */
uint16_t vGet_Motor_Steps_Per_Revolution(void)
{
	// 电机参数配置（请根据实际电机修改）
	float step_angle = 1.8f;      // 步进角度（度）
	uint16_t microstep = 64;      // 细分数

	uint16_t steps = vCalculate_Steps_Per_Revolution(step_angle, microstep);

	printf("Motor parameters:\r\n");
	printf("- Step angle: %.1f degrees\r\n", step_angle);
	printf("- Microstep: %d\r\n", microstep);
	printf("- Steps per revolution: %d\r\n", steps);

	return steps;
}

/**
 * @brief  X轴和Y轴交替转动控制函数
 * @param  steps: 每次转动的步数
 * @param  speed: 转动速度(RPM)
 * @param  acc: 加速度
 * @param  delay_ms: 轴间切换延时(毫秒)
 * @retval None
 * @note   实现X轴和Y轴的交替转动，每完成一轮后改变方向
 */
void vAlternating_Motor_Control(uint16_t steps, uint16_t speed, uint8_t acc, uint16_t delay_ms)
{
	static uint8_t current_axis = 0;  // 0=X轴, 1=Y轴
	static uint8_t current_dir = 0;   // 0=正转, 1=反转
	static uint32_t cycle_count = 0;  // 循环计数器

	// 假设12800步为一圈（根据实际电机参数调整）
	uint16_t steps_per_revolution = 12800;
	float revolutions = (float)steps / steps_per_revolution;

	cycle_count++;

	if(current_axis == 0) {
		// X轴转动
		printf("=== Cycle %d: X-axis ===\r\n", cycle_count);
		printf("X-axis: %s %.2f revolutions (%d steps, Speed: %d RPM)\r\n",
		       (current_dir == 0) ? "CW" : "CCW", revolutions, steps, speed);

		// 使用位置控制模式
		Emm_V5_Pos_Control(M1_addr, current_dir, speed, acc, steps, false, false);

		// 更新步进计数器
		if(current_dir == 0)
			Usartdata.x_step += steps;
		else
			Usartdata.x_step -= steps;

	} else {
		// Y轴转动
		printf("=== Cycle %d: Y-axis ===\r\n", cycle_count);
		printf("Y-axis: %s %.2f revolutions (%d steps, Speed: %d RPM)\r\n",
		       (current_dir == 0) ? "CW" : "CCW", revolutions, steps, speed);

		// 使用位置控制模式
		Emm_V5_Pos_Control(M0_addr, current_dir, speed, acc, steps, false, false);

		// 更新步进计数器
		if(current_dir == 0)
			Usartdata.y_step += steps;
		else
			Usartdata.y_step -= steps;
	}

	// 等待运动完成
	uint16_t motion_delay = (steps * 60 * 1000) / (speed * 200) + 100;  // 估算运动时间
	if(motion_delay > 2000) motion_delay = 2000;  // 限制最大延时
	Delayms(motion_delay);

	// 切换到下一个轴
	current_axis = 1 - current_axis;

	// 每完成一轮X-Y轴交替后，改变方向
	if(current_axis == 0) {
		current_dir = 1 - current_dir;
		printf("Direction changed to %s\r\n", (current_dir == 0) ? "CW" : "CCW");
	}

	// 轴间切换延时
	Delayms(delay_ms);

	// 计算当前位置的圈数
	float x_revolutions = (float)Usartdata.x_step / steps_per_revolution;
	float y_revolutions = (float)Usartdata.y_step / steps_per_revolution;

	printf("Current position - X: %d steps (%.2f rev), Y: %d steps (%.2f rev)\r\n",
	       Usartdata.x_step, x_revolutions, Usartdata.y_step, y_revolutions);
	printf("---------------------------------------------------\r\n");
}

/**
 * @brief  连续交替转动模式
 * @param  cycles: 交替循环次数
 * @param  steps: 每次转动的步数
 * @param  speed: 转动速度(RPM)
 * @param  acc: 加速度
 * @param  cycle_delay_ms: 每个循环间的延时
 * @retval None
 * @note   执行指定次数的X-Y轴交替转动
 */
void vContinuous_Alternating_Motion(uint8_t cycles, uint16_t steps, uint16_t speed, uint8_t acc, uint16_t cycle_delay_ms)
{
	printf("Starting continuous alternating motion:\r\n");
	printf("- Cycles: %d\r\n", cycles);
	printf("- Steps per axis: %d\r\n", steps);
	printf("- Speed: %d RPM\r\n", speed);
	printf("- Acceleration: %d\r\n", acc);

	for(uint8_t i = 0; i < cycles; i++) {
		printf("\n=== Cycle %d/%d ===\r\n", i+1, cycles);

		// X轴转动
		vAlternating_Motor_Control(steps, speed, acc, cycle_delay_ms/2);

		// Y轴转动
		vAlternating_Motor_Control(steps, speed, acc, cycle_delay_ms/2);

		// 检查回原点按键
		if(Key_Getlevel()) {
			printf("Return to zero requested, stopping alternating motion\r\n");
			vInterpolation_Point_Serial(0, 0);
			break;
		}
	}

	printf("Continuous alternating motion completed\r\n");
}
