# X轴Y轴交替转动控制说明

## 概述

已成功实现X轴和Y轴电机的交替转动控制，使用串口控制模式。系统支持自动交替转动和相机数据优先处理。

## 功能特点

### 1. 交替转动模式
- **X轴和Y轴依次转动**：先X轴转动指定步数，再Y轴转动相同步数
- **方向自动切换**：每完成一轮X-Y轴交替后，自动改变转动方向
- **参数可配置**：步数、速度、加速度、延时等参数均可调整

### 2. 相机数据优先处理
- **实时响应**：接收到相机数据时，立即中断交替转动
- **精确定位**：使用插补算法精确移动到目标位置
- **自动恢复**：处理完相机数据后，自动恢复交替转动模式

### 3. 状态监控
- **实时反馈**：通过USART2输出详细的运动状态信息
- **位置跟踪**：实时显示X轴和Y轴的累计步数
- **LED指示**：运动完成时LED闪烁提示

## 核心函数

### 1. 单次交替转动函数
```c
void vAlternating_Motor_Control(uint16_t steps, uint16_t speed, uint8_t acc, uint16_t delay_ms)
```
**参数说明：**
- `steps`: 每次转动的步数
- `speed`: 转动速度(RPM)，范围0-5000
- `acc`: 加速度，范围0-255
- `delay_ms`: 轴间切换延时(毫秒)

**功能：**
- 执行一次X轴或Y轴转动
- 自动切换轴和方向
- 更新位置计数器

### 2. 连续交替转动函数
```c
void vContinuous_Alternating_Motion(uint8_t cycles, uint16_t steps, uint16_t speed, uint8_t acc, uint16_t cycle_delay_ms)
```
**参数说明：**
- `cycles`: 交替循环次数
- `steps`: 每次转动的步数
- `speed`: 转动速度(RPM)
- `acc`: 加速度
- `cycle_delay_ms`: 每个循环间的延时

**功能：**
- 执行指定次数的完整交替循环
- 支持按键中断回零
- 自动统计运动状态

## 使用方法

### 1. 基本配置

在main.c中配置交替转动参数：

```c
uint16_t steps_per_cycle = 200;   // 每次转动200步
uint16_t motor_speed = 300;       // 速度300 RPM
uint8_t acceleration = 30;        // 加速度30
uint16_t cycle_delay = 1000;      // 轴间延时1秒
```

### 2. 主循环实现

```c
for(;;)
{
    // 优先处理相机数据
    vData_Get();
    if(Usartdata.x != 0 || Usartdata.y != 0) {
        vInterpolation_Point_Serial(Usartdata.x, Usartdata.y);
        Usartdata.x = 0;
        Usartdata.y = 0;
        continue;
    }

    // 定时执行交替转动
    if(loop_counter >= alternating_interval) {
        vAlternating_Motor_Control(steps_per_cycle, motor_speed, acceleration, cycle_delay);
        loop_counter = 0;
    }
    loop_counter++;
}
```

### 3. 连续运动示例

```c
// 执行10个循环的交替转动
vContinuous_Alternating_Motion(10, 100, 250, 20, 500);
```

## 运动模式说明

### 交替转动序列

```
循环1: X轴正转 → Y轴正转
循环2: X轴反转 → Y轴反转
循环3: X轴正转 → Y轴正转
循环4: X轴反转 → Y轴反转
...
```

### 方向控制逻辑

- **0**: 正转(CW - Clockwise)
- **1**: 反转(CCW - Counter-Clockwise)
- 每完成一轮X-Y轴交替后，方向自动切换

### 位置计数

- **X轴计数器**: `Usartdata.x_step`
- **Y轴计数器**: `Usartdata.y_step`
- 正转时计数器递增，反转时计数器递减

## 参数调优建议

### 1. 步数设置
```c
// 小幅运动
uint16_t steps = 50;    // 适合精细调试

// 中等运动
uint16_t steps = 200;   // 适合常规测试

// 大幅运动
uint16_t steps = 500;   // 适合范围测试
```

### 2. 速度设置
```c
// 低速运动（高精度）
uint16_t speed = 100;   // 100 RPM

// 中速运动（平衡）
uint16_t speed = 300;   // 300 RPM

// 高速运动（高效率）
uint16_t speed = 800;   // 800 RPM
```

### 3. 加速度设置
```c
// 平滑启动
uint8_t acc = 10;       // 低加速度

// 标准启动
uint8_t acc = 30;       // 中等加速度

// 快速启动
uint8_t acc = 100;      // 高加速度
```

## 调试信息输出

系统通过USART2输出详细的调试信息：

```
System ready, Serial Motor Control Mode
X-axis and Y-axis alternating rotation started...
Starting alternating motor rotation:
- Steps per cycle: 200
- Motor speed: 300 RPM
- Acceleration: 30
- X-axis address: 2, Y-axis address: 1

X-axis: CW 200 steps (Speed: 300 RPM)
Current position - X: 200, Y: 0
Y-axis: CW 200 steps (Speed: 300 RPM)
Direction changed to CCW
Current position - X: 200, Y: 200

Camera data received: (150, 250), executing movement...
Current position - X: 350, Y: 450

X-axis: CCW 200 steps (Speed: 300 RPM)
Current position - X: 150, Y: 450
```

## 注意事项

### 1. 硬件连接
- 确保USART1正确连接到电机驱动器
- 确保USART2正确连接到调试终端
- 检查电机驱动器电源和地线连接

### 2. 参数限制
- 速度范围：0-5000 RPM
- 加速度范围：0-255
- 步数范围：1-65535
- 延时范围：1-65535毫秒

### 3. 系统优先级
1. **最高优先级**：相机数据处理
2. **中等优先级**：按键回零检测
3. **低优先级**：交替转动执行

### 4. 故障排除
- **电机不动**：检查串口连接和电机使能
- **运动不准确**：调整速度和加速度参数
- **通信错误**：检查波特率和电机地址设置
- **位置偏差**：检查步数计算和方向控制

## 扩展功能

### 1. 自定义运动模式
```c
// 可以根据需要实现其他运动模式
void vCustom_Motion_Pattern(void);
```

### 2. 位置校准
```c
// 定期回零校准
if(total_steps > 10000) {
    vInterpolation_Point_Serial(0, 0);  // 回到原点
}
```

### 3. 运动状态保存
```c
// 保存当前位置状态
typedef struct {
    int32_t x_pos;
    int32_t y_pos;
    uint8_t last_dir;
} MotionState_t;
```

现在你的系统已经实现了完整的X轴Y轴交替转动控制！
