/*
 * motor_usage_example.c - 电机控制使用示例
 * 演示如何使用新的串口控制模式
 */

#include "motor.h"
#include "board.h"

/**
 * @brief 串口控制模式使用示例
 */
void Serial_Motor_Example(void)
{
    printf("=== 串口控制模式示例 ===\n");
    
    // 1. 初始化电机系统（已在board_init中调用vDriverInit）
    // vDriverInit(); // 这会自动调用vMotor_Serial_Init()
    
    // 2. 单点运动示例
    printf("执行单点运动到(1000, 500)\n");
    vInterpolation_Point_Serial(1000, 500);
    
    // 3. 多点运动示例
    int16_t test_points[5][2] = {
        {500, 300},
        {1000, 600},
        {800, 800},
        {200, 400},
        {0, 0}  // 回到原点
    };
    
    printf("执行多点运动\n");
    for(int i = 0; i < 5; i++) {
        printf("移动到点(%d, %d)\n", test_points[i][0], test_points[i][1]);
        vInterpolation_Point_Serial(test_points[i][0], test_points[i][1]);
        Delayms(500); // 等待500ms
    }
    
    // 4. 使用图像坐标转换
    int16_t image_coords[3][2] = {
        {100, 150},
        {200, 250},
        {50, 100}
    };
    
    printf("执行图像坐标转换运动\n");
    vImage_To_Actual(image_coords, 3);
    
    printf("=== 示例完成 ===\n");
}

/**
 * @brief 脉冲控制模式使用示例（需要将CURRENT_MOTOR_CONTROL_MODE改为MOTOR_CONTROL_MODE_PULSE）
 */
void Pulse_Motor_Example(void)
{
    printf("=== 脉冲控制模式示例 ===\n");
    
    // 注意：要使用脉冲控制模式，需要在motor.h中修改：
    // #define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_PULSE
    
    // 单点运动示例
    printf("执行单点运动到(1000, 500)\n");
    vInterpolation_Point(1000, 500);
    
    // 多点运动示例
    int16_t test_points[3][2] = {
        {500, 300},
        {1000, 600},
        {0, 0}
    };
    
    printf("执行多点运动\n");
    for(int i = 0; i < 3; i++) {
        printf("移动到点(%d, %d)\n", test_points[i][0], test_points[i][1]);
        vInterpolation_Point(test_points[i][0], test_points[i][1]);
        Delayms(500);
    }
    
    printf("=== 示例完成 ===\n");
}

/**
 * @brief 主函数中的调用示例
 */
void Motor_Test_Main(void)
{
    // 系统初始化
    board_init();  // 这会调用vDriverInit()
    
    printf("电机控制系统初始化完成\n");
    printf("当前控制模式: %s\n", 
           (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL) ? "串口控制" : "脉冲控制");
    
    // 根据当前模式运行对应示例
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
    Serial_Motor_Example();
#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
    Pulse_Motor_Example();
#endif
    
    // 主循环
    while(1) {
        // 处理串口接收的坐标数据
        vData_Get();
        
        // 如果接收到新的坐标数据，执行运动
        if(Usartdata.x != 0 || Usartdata.y != 0) {
            printf("接收到坐标: (%d, %d)\n", Usartdata.x, Usartdata.y);
            
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
            vInterpolation_Point_Serial(Usartdata.x, Usartdata.y);
#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
            vInterpolation_Point(Usartdata.x, Usartdata.y);
#endif
            
            // 清除坐标数据
            Usartdata.x = 0;
            Usartdata.y = 0;
        }
        
        Delayms(10); // 主循环延时
    }
}
