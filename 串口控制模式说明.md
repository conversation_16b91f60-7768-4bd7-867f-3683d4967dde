# 电机控制系统串口模式替换说明

## 概述

已成功将motor.c中的脉冲控制模式替换为串口控制模式，使用Emm_V5电机驱动库实现更高精度和更可靠的电机控制。

## 主要改动

### 1. 控制模式选择机制

在`motor.h`中添加了控制模式选择宏：

```c
#define MOTOR_CONTROL_MODE_PULSE  0   // 脉冲控制模式
#define MOTOR_CONTROL_MODE_SERIAL 1   // 串口控制模式
#define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_SERIAL  // 当前模式
```

### 2. 新增的串口控制函数

#### 2.1 电机初始化函数
```c
void vMotor_Serial_Init(void)
```
- 使能X轴和Y轴电机
- 设置当前位置为零点
- 初始化步进计数器

#### 2.2 优化的插补函数
```c
uint8_t vInterpolation_Point_Serial(int16_t POINT_X, int16_t POINT_Y)
```
- 使用多轴同步运动实现高效控制
- 自动计算运动方向和步数
- 支持回原点中断功能

#### 2.3 单步运动函数（串口版本）
```c
void vOne_Step_X(uint8_t dir)  // X轴单步运动
void vOne_Step_Y(uint8_t dir)  // Y轴单步运动
```
- 参数dir：0为正转，1为反转
- 使用串口发送位置控制命令

### 3. 兼容性设计

系统同时支持两种控制模式，通过条件编译实现：

- **脉冲控制模式**：保留原有的GPIO脉冲控制功能
- **串口控制模式**：使用Emm_V5库的串口控制功能

## 使用方法

### 切换控制模式

在`motor.h`中修改宏定义：

```c
// 使用串口控制模式
#define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_SERIAL

// 或使用脉冲控制模式
#define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_PULSE
```

### 基本使用示例

```c
// 1. 系统初始化（在main函数中）
board_init();  // 自动调用vDriverInit()

// 2. 单点运动
vInterpolation_Point_Serial(1000, 500);  // 移动到(1000, 500)

// 3. 多点运动
int16_t points[3][2] = {{100, 200}, {300, 400}, {0, 0}};
for(int i = 0; i < 3; i++) {
    vInterpolation_Point_Serial(points[i][0], points[i][1]);
    Delayms(500);  // 等待运动完成
}

// 4. 图像坐标转换运动
int16_t image_coords[2][2] = {{150, 200}, {250, 300}};
vImage_To_Actual(image_coords, 2);
```

## 优势对比

### 串口控制模式优势：

1. **精度更高**：闭环控制，自动纠错
2. **速度更快**：硬件加速，无软件延时
3. **功能丰富**：支持速度控制、加速度控制、堵转保护
4. **可靠性好**：抗干扰能力强
5. **代码简洁**：无需复杂的脉冲生成逻辑

### 脉冲控制模式优势：

1. **实时性好**：直接GPIO控制，响应快
2. **资源占用少**：不依赖串口通信
3. **调试方便**：可直接观察GPIO波形

## 参数配置

### 串口控制参数

在`vOne_Step_X`和`vOne_Step_Y`函数中可调整：

```c
Emm_V5_Pos_Control(M1_addr, dir, 100, 10, 1, false, false);
//                 地址    方向  速度 加速度 步数  相对  同步
```

- **速度**：100 RPM（可调整范围0-5000）
- **加速度**：10（可调整范围0-255，0为直接启动）
- **步数**：1（单步运动）

### 同步运动参数

在`vInterpolation_Point_Serial`函数中可调整：

```c
Emm_V5_Pos_Control(M1_addr, x_dir, 200, 20, abs_x, false, true);
//                 地址     方向   速度 加速度 步数   相对  同步
```

- **速度**：200 RPM（双轴同步运动建议使用较高速度）
- **加速度**：20（较高加速度确保同步性）

## 注意事项

1. **串口通信**：确保USART1正常工作，波特率9600
2. **电机地址**：确认M0_addr(Y轴)=1，M1_addr(X轴)=2
3. **延时调整**：根据实际运动距离调整延时时间
4. **错误处理**：建议添加运动完成状态检测
5. **电源要求**：串口控制模式需要电机驱动器正常供电

## 故障排除

1. **电机不动**：检查串口连接和电机使能状态
2. **运动不准确**：调整速度和加速度参数
3. **同步性差**：确保使用同步运动功能
4. **通信错误**：检查串口配置和电机地址设置

## 扩展功能

系统还支持以下高级功能：

1. **速度模式控制**：`Emm_V5_Vel_Control()`
2. **回零功能**：`Emm_V5_Origin_Trigger_Return()`
3. **堵转保护**：`Emm_V5_Reset_Clog_Pro()`
4. **参数读取**：`Emm_V5_Read_Sys_Params()`

详细使用方法请参考`motor_usage_example.c`文件。
