#include "board.h"
#include "delay.h"
#include "string.h"
/**********************************************************
*** ZDT_X42_V2.0步进闭环控制例程
*** 编写作者：ZHANGDATOU
*** 技术支持：张大头闭环伺服
*** 淘宝店铺：https://zhangdatou.taobao.com
*** CSDN博客：http s://blog.csdn.net/zhangdatou666
*** qq交流群：262438510
**********************************************************/

// 接收缓冲区
#define RX_BUFFER_SIZE 64
#define PACKET_SIZE 10

static uint8_t rx_buffer[RX_BUFFER_SIZE];
static uint8_t rx_index = 0;
static CameraData_t camera_data = {0};

/**
	* @brief   配置NVIC控制器
	* @param   无
	* @retval  无
	*/
void nvic_init(void)
{
	// 4bit抢占优先级位
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);
	NVIC_InitTypeDef NVIC_InitStructure;

	// USART1中断配置（电机控制）
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_Init(&NVIC_InitStructure);

	// USART2中断配置（调试输出，可选）
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
	NVIC_Init(&NVIC_InitStructure);

	// USART3中断配置（相机数据接收）
	NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
	NVIC_Init(&NVIC_InitStructure);
}

/**
	*	@brief		外设时钟初始化
	*	@param		无
	*	@retval		无
	*/
void clock_init(void)
{
	// 使能GPIOA、GPIOB、AFIO外设时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_AFIO, ENABLE);

	// 使能USART1外设时钟（用于电机控制）
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);

	// 使能USART2外设时钟（用于printf调试）
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);

	// 禁用JTAG
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE);
}

/**
	* @brief   初始化USART1（专用于电机控制）
	* @param   无
	* @retval  无
	*/
void usart1_motor_init(void)
{
/**********************************************************
***	初始化USART1引脚（电机控制专用）
**********************************************************/
	// PA9 - USART1_TX
	GPIO_InitTypeDef  GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;				/* 复用推挽输出 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// PA10 - USART1_RX
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;					/* 浮空输入 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);

/**********************************************************
***	初始化USART1（电机控制专用）
**********************************************************/
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate = 115200;					// 电机控制波特率
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
	USART_Init(USART1, &USART_InitStructure);

/**********************************************************
***	清除USART1中断
**********************************************************/
	USART1->SR; USART1->DR;
	USART_ClearITPendingBit(USART1, USART_IT_RXNE);

/**********************************************************
***	使能USART1中断（如果需要接收电机反馈）
**********************************************************/
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
	USART_ITConfig(USART1, USART_IT_IDLE, ENABLE);

/**********************************************************
***	使能USART1
**********************************************************/
	USART_Cmd(USART1, ENABLE);
}

/**
	* @brief   初始化USART2（专用于printf调试）
	* @param   无
	* @retval  无
	*/
void usart2_debug_init(void)
{
/**********************************************************
***	初始化USART2引脚（调试专用）
**********************************************************/
	// PA2 - USART2_TX
	GPIO_InitTypeDef  GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;				/* 复用推挽输出 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// PA3 - USART2_RX
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;					/* 浮空输入 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);

/**********************************************************
***	初始化USART2（调试专用）
**********************************************************/
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate = 115200;				// 调试高波特率
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
	USART_Init(USART2, &USART_InitStructure);

/**********************************************************
***	使能USART2
**********************************************************/
	USART_Cmd(USART2, ENABLE);
}

/**
	* @brief   初始化USART（兼容性函数）
	* @param   无
	* @retval  无
	*/
void usart_init(void)
{
	// 初始化USART2用于调试输出
	usart2_debug_init();

	// 初始化USART1用于电机控制
	usart1_motor_init();
}

#define KEY_RCC (RCC_APB2Periph_GPIOA)
#define KEY_PIN_PORT (GPIOA)
#define KEY_PIN (GPIO_Pin_0)
void vKey_Init(void)
{
	RCC_APB2PeriphClockCmd(KEY_RCC,ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = KEY_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_Init(KEY_PIN_PORT,&GPIO_InitStructure);
}

bool Key_Getlevel(void)
{
	if(!GPIO_ReadInputDataBit(KEY_PIN_PORT,KEY_PIN))
	{
		delay_ms(10);
		while(!GPIO_ReadInputDataBit(KEY_PIN_PORT,KEY_PIN));
		delay_ms(10);
		return true;
	}
	return false;
}

// LED相关宏定义
#define LED_RCC (RCC_APB2Periph_GPIOA)
#define LED_PIN_PORT (GPIOA)
#define LED_PIN (GPIO_Pin_1)

// LED初始化函数
void vLED_Init(void)
{
	RCC_APB2PeriphClockCmd(LED_RCC, ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;  // 推挽输出
	GPIO_InitStructure.GPIO_Pin = LED_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_Init(LED_PIN_PORT, &GPIO_InitStructure);

	// LED默认熄灭（PA1低电平熄灭，高电平点亮）
	GPIO_ResetBits(LED_PIN_PORT, LED_PIN);
}

// LED闪烁函数
void vLED_Blink(uint8_t times)
{
	for(uint8_t i = 0; i < times; i++)
	{
		GPIO_SetBits(LED_PIN_PORT, LED_PIN);    // 点亮LED（PA1高电平点亮）
		delay_ms(200);  // 亮200ms
		GPIO_ResetBits(LED_PIN_PORT, LED_PIN);  // 熄灭LED（PA1低电平熄灭）
		delay_ms(200);  // 灭200ms
	}
}

#define USART_PORT GPIOB
#define USART_Hardware USART3
#define USART_TX GPIO_Pin_10
#define USART_RX GPIO_Pin_11
void vUsart_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	GPIO_InitTypeDef USART_GPIOStruct;
	USART_GPIOStruct.GPIO_Mode = GPIO_Mode_AF_PP;
	USART_GPIOStruct.GPIO_Pin = USART_TX;
	USART_GPIOStruct.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(USART_PORT,&USART_GPIOStruct);
	USART_GPIOStruct.GPIO_Mode = GPIO_Mode_IPU;
	USART_GPIOStruct.GPIO_Pin = USART_RX;
	USART_GPIOStruct.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(USART_PORT,&USART_GPIOStruct);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3,ENABLE);
	USART_InitTypeDef USART_Struct;
	USART_StructInit(&USART_Struct);
	USART_Struct.USART_BaudRate = 115200;
	USART_Struct.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_Struct.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
	USART_Struct.USART_Parity = USART_Parity_No;
	USART_Struct.USART_StopBits = USART_StopBits_1;
	USART_Struct.USART_WordLength = USART_WordLength_8b;
	USART_Init(USART_Hardware,&USART_Struct);

	// 正确使能接收中断
	USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);

	USART_Cmd(USART_Hardware,ENABLE);
}
/**
	*	@brief		板载初始化
	*	@param		无
	*	@retval		无
	*/
void board_init(void)
{
	nvic_init();
	clock_init();
	usart_init();
	vUsart_Init();
	vDriverInit();

	vKey_Init();
	vLED_Init();  // LED initialization
}

// 外部变量声明
extern int original_fputc_enabled;

int fputc(int chr,FILE *p)
{
	// 如果printf输出被禁用，直接返回
	if(!original_fputc_enabled) {
		return chr;
	}

	USART_SendData(USART2,(uint8_t)chr);  // 使用USART2进行printf输出
	while(USART_GetFlagStatus(USART2,USART_FLAG_TXE)==RESET);
	return chr;
}

#define wait_head (0)
#define wait_Xpoint (1)
#define wait_Ypoint (2)
#define wait_num (3)
#define wait_Tail (4)

#define RECEIVE_SIZE (64)
uint8_t Receive_Finish = 0;
uint8_t Receive_String[RECEIVE_SIZE];//接受数据
uint8_t Receive_Flag = 0;//接受完成标志位
uint8_t Receive_state = 0;
uint8_t XendYstart = 0;
uint8_t Numstart=0;

void USART3_IRQHandler(void)
{
	if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uint8_t received_byte = USART_ReceiveData(USART3);

        // 查找帧头 0xFF 0xF8
        if(rx_index == 0 && received_byte == 0xFF)
        {
            rx_buffer[rx_index] = received_byte;
            rx_index = 1;
        }
        else if(rx_index == 1 && received_byte == 0xF8)
        {
            rx_buffer[rx_index] = received_byte;
            rx_index = 2;
        }
        else if(rx_index >= 2 && rx_index < PACKET_SIZE - 1)
        {
            rx_buffer[rx_index] = received_byte;
            rx_index++;
        }
        else if(rx_index == PACKET_SIZE - 1 && received_byte == 0xFE)
        {
            rx_buffer[rx_index] = received_byte;
            // 接收到完整数据包，解析数据
            Camera_ParseData(rx_buffer);
            rx_index = 0;
        }
        else
        {
            // 数据错误，重新开始
            rx_index = 0;
        }

        USART_ClearITPendingBit(USART3, USART_IT_RXNE);
	}

}
#define POINT_CNT (1)  // 改为单点接收
_DATAPoint Usartdata = {0,};
int16_t usart_point[1][2]={{0,},};  // 单点坐标数组

/**
 * @brief 解析相机数据包
 * @param data 接收到的数据包
 */
void Camera_ParseData(uint8_t *data)
{
    // 检查帧头和帧尾
    if(data[0] == 0xFF && data[1] == 0xF8 && data[9] == 0xFE)
    {
        // 解析坐标 (小端序)
        camera_data.center_x = data[2] | (data[3] << 8);
        camera_data.center_y = data[4] | (data[5] << 8);

        // 解析误差值 (有符号字节)
        camera_data.err_x = (int8_t)data[6];
        camera_data.err_y = (int8_t)data[7];

        // 检查固定标识位
        if(data[8] == 0x01)
        {
            camera_data.valid = 1;
        }
        else
        {
            camera_data.valid = 0;
        }
    }
}

void vData_Get(void)//数据转化
{
    // 如果有新的有效相机数据
    if(camera_data.valid)
    {
        // 将相机数据转换为Usartdata格式
        Usartdata.x = camera_data.center_x;
        Usartdata.y = camera_data.center_y;
        Usartdata.num = 0;  // 单点数据，编号为0

        // 打印接收到的数据
        printf("(%d, %d), Error: X=%d, Y=%d\r\n",
               Usartdata.x, Usartdata.y,
               camera_data.err_x, camera_data.err_y);

        // LED闪烁一次表示接收到数据
        vLED_Blink(1);

        // 清除有效标志
        camera_data.valid = 0;
    }
}

void vSingle_Point_Receive(void)
{
    // 清空坐标数组
    usart_point[0][0] = 0;
    usart_point[0][1] = 0;

    // 等待接收单个坐标点
    uint8_t timeout = 100;
    while(timeout-- > 0)
    {
        vData_Get();
        if(camera_data.valid == 0 && Usartdata.x != 0 && Usartdata.y != 0)
        {
            // 将接收到的点存储到数组第一个位置
            usart_point[0][0] = Usartdata.x;
            usart_point[0][1] = Usartdata.y;
            break;
        }
        delay_ms(10);
    }
}

/**
 * @brief 获取最新的相机数据
 * @return 相机数据结构体指针
 */
CameraData_t* Camera_GetData(void)
{
    return &camera_data;
}

/**
 * @brief 检查是否有新的有效数据
 * @return 1-有新数据, 0-无新数据
 */
uint8_t Camera_HasNewData(void)
{
    if(camera_data.valid)
    {
        camera_data.valid = 0; // 清除标志
        return 1;
    }
    return 0;
}

/**
 * @brief 重置接收状态
                                               */
void reset_receive_state(void)
{
    rx_index = 0;
    camera_data.valid = 0;
}
