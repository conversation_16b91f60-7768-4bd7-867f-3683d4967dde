# 电机一圈转动配置说明

## 概述

已成功配置电机交替转动一圈的功能。系统会自动计算电机一圈所需的步数，并实现X轴和Y轴的完整圈数转动。

## 电机参数配置

### 1. 步数计算公式

```
一圈步数 = 360° / (步进角度 / 细分数)
```

### 2. 常见电机配置

#### 标准1.8度步进电机
```c
// 64细分配置
float step_angle = 1.8f;      // 1.8度/步
uint16_t microstep = 64;      // 64细分
// 计算结果：360/(1.8/64) = 12800步/圈

// 32细分配置
float step_angle = 1.8f;      // 1.8度/步
uint16_t microstep = 32;      // 32细分
// 计算结果：360/(1.8/32) = 6400步/圈

// 16细分配置
float step_angle = 1.8f;      // 1.8度/步
uint16_t microstep = 16;      // 16细分
// 计算结果：360/(1.8/16) = 3200步/圈
```

#### 高精度0.9度步进电机
```c
// 64细分配置
float step_angle = 0.9f;      // 0.9度/步
uint16_t microstep = 64;      // 64细分
// 计算结果：360/(0.9/64) = 25600步/圈

// 32细分配置
float step_angle = 0.9f;      // 0.9度/步
uint16_t microstep = 32;      // 32细分
// 计算结果：360/(0.9/32) = 12800步/圈
```

### 3. 修改电机参数

在`motor.c`文件的`vGet_Motor_Steps_Per_Revolution()`函数中修改：

```c
uint16_t vGet_Motor_Steps_Per_Revolution(void)
{
    // 电机参数配置（请根据实际电机修改）
    float step_angle = 1.8f;      // 步进角度（度）- 修改这里
    uint16_t microstep = 64;      // 细分数 - 修改这里
    
    uint16_t steps = vCalculate_Steps_Per_Revolution(step_angle, microstep);
    
    printf("Motor parameters:\r\n");
    printf("- Step angle: %.1f degrees\r\n", step_angle);
    printf("- Microstep: %d\r\n", microstep);
    printf("- Steps per revolution: %d\r\n", steps);
    
    return steps;
}
```

## 当前运动配置

### 1. 转动参数
```c
uint16_t steps_per_revolution = 12800;  // 一圈步数（自动计算）
uint16_t steps_per_cycle = steps_per_revolution;  // 每次转动一圈
uint16_t motor_speed = 200;       // 转动速度200 RPM
uint8_t acceleration = 20;        // 加速度20
uint16_t cycle_delay = 2000;      // 轴间切换延时2秒
```

### 2. 运动序列
```
循环1: X轴正转1圈 → 等待2秒 → Y轴正转1圈 → 等待2秒
循环2: X轴反转1圈 → 等待2秒 → Y轴反转1圈 → 等待2秒
循环3: X轴正转1圈 → 等待2秒 → Y轴正转1圈 → 等待2秒
...
```

### 3. 运动时间估算
```
单圈运动时间 ≈ (步数 × 60) / (速度RPM × 每圈步数) 秒

例如：12800步，200RPM
运动时间 ≈ (12800 × 60) / (200 × 12800) ≈ 0.3秒

加上加速减速时间，实际约0.5-1秒/圈
```

## 调试输出示例

```
System ready, Serial Motor Control Mode
X-axis and Y-axis alternating rotation started...

Motor parameters:
- Step angle: 1.8 degrees
- Microstep: 64
- Steps per revolution: 12800

Starting alternating motor rotation (1 revolution per cycle):
- Steps per revolution: 12800
- Steps per cycle: 12800 (1 full revolution)
- Motor speed: 200 RPM
- Acceleration: 20
- Cycle delay: 2000 ms
- X-axis address: 2, Y-axis address: 1

=== Cycle 1: X-axis ===
X-axis: CW 1.00 revolutions (12800 steps, Speed: 200 RPM)
Current position - X: 12800 steps (1.00 rev), Y: 0 steps (0.00 rev)
---------------------------------------------------

=== Cycle 2: Y-axis ===
Y-axis: CW 1.00 revolutions (12800 steps, Speed: 200 RPM)
Direction changed to CCW
Current position - X: 12800 steps (1.00 rev), Y: 12800 steps (1.00 rev)
---------------------------------------------------

=== Cycle 3: X-axis ===
X-axis: CCW 1.00 revolutions (12800 steps, Speed: 200 RPM)
Current position - X: 0 steps (0.00 rev), Y: 12800 steps (1.00 rev)
---------------------------------------------------
```

## 参数调优建议

### 1. 速度调整
```c
// 慢速转动（高精度，观察效果明显）
uint16_t motor_speed = 100;   // 100 RPM，约6秒/圈

// 中速转动（平衡速度和精度）
uint16_t motor_speed = 200;   // 200 RPM，约3秒/圈

// 快速转动（高效率）
uint16_t motor_speed = 400;   // 400 RPM，约1.5秒/圈
```

### 2. 加速度调整
```c
// 平滑启动（减少震动）
uint8_t acceleration = 10;    // 低加速度

// 标准启动
uint8_t acceleration = 20;    // 中等加速度

// 快速启动
uint8_t acceleration = 50;    // 高加速度
```

### 3. 延时调整
```c
// 快速切换
uint16_t cycle_delay = 500;   // 0.5秒延时

// 标准切换
uint16_t cycle_delay = 2000;  // 2秒延时

// 慢速切换（便于观察）
uint16_t cycle_delay = 5000;  // 5秒延时
```

## 不同电机的配置示例

### 1. 标准NEMA17电机（1.8度，200步/圈）
```c
// 在vGet_Motor_Steps_Per_Revolution()中设置：
float step_angle = 1.8f;
uint16_t microstep = 64;      // 12800步/圈
// 或
uint16_t microstep = 32;      // 6400步/圈
// 或
uint16_t microstep = 16;      // 3200步/圈
```

### 2. 高精度电机（0.9度，400步/圈）
```c
// 在vGet_Motor_Steps_Per_Revolution()中设置：
float step_angle = 0.9f;
uint16_t microstep = 64;      // 25600步/圈
// 或
uint16_t microstep = 32;      // 12800步/圈
```

### 3. 大扭矩电机（1.8度，特殊细分）
```c
// 在vGet_Motor_Steps_Per_Revolution()中设置：
float step_angle = 1.8f;
uint16_t microstep = 128;     // 25600步/圈（超高精度）
```

## 故障排除

### 1. 转动角度不准确
- 检查电机步进角度设置
- 确认细分数配置
- 验证电机驱动器细分设置

### 2. 转动速度过快/过慢
- 调整motor_speed参数
- 检查电机负载情况
- 确认电源供电充足

### 3. 运动不平滑
- 降低加速度参数
- 增加cycle_delay延时
- 检查机械连接是否松动

### 4. 位置累积误差
- 定期执行回零操作
- 检查电机是否丢步
- 确认负载在电机承受范围内

## 测试建议

### 1. 参数验证测试
```c
// 在main函数开始时添加测试代码
printf("Testing motor parameters...\r\n");
uint16_t test_steps = vGet_Motor_Steps_Per_Revolution();
printf("Calculated steps per revolution: %d\r\n", test_steps);
```

### 2. 单轴测试
```c
// 先测试单个轴的一圈转动
vAlternating_Motor_Control(steps_per_revolution, 100, 10, 3000);
```

### 3. 逐步增加速度测试
```c
// 从低速开始测试，逐步增加速度
for(uint16_t speed = 50; speed <= 500; speed += 50) {
    printf("Testing speed: %d RPM\r\n", speed);
    vAlternating_Motor_Control(steps_per_revolution, speed, 20, 2000);
    Delayms(5000);  // 等待5秒观察效果
}
```

现在你的电机将会完整地转动一圈，可以清楚地观察到转动效果！
