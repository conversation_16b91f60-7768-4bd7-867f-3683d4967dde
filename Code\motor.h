#ifndef __MOTOR_H
#define __MOTOR_H

#include "stm32f10x.h"
#include "Emm_V5.h"

#define Limit_Out(input,max,min) \
	((input)>(max)?(max):((input)<(min)?(min):(input)))

typedef struct _PID_Struct_
{
	float error[3];
	float kp;
	float ki;
	float kd;
	float pout;
	float iout;
	float dout;
	float out[2];
}PID_Struct_;

// 控制模式选择宏定义
#define MOTOR_CONTROL_MODE_PULSE  0   // 脉冲控制模式
#define MOTOR_CONTROL_MODE_SERIAL 1   // 串口控制模式

// 当前控制模式（可以通过修改这个值来切换模式）
#define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_SERIAL

void vDriverInit(void);
void Delayms(u16 ms);

int16_t PID_Control(PID_Struct_ *_PID_,int16_t error);

void vImage_To_Actual(int16_t Image[][2],uint8_t cnt);
uint8_t vInterpolation_Point(int16_t POINT_X,int16_t POINT_Y);//原始插补函数
void vInterpolation_Move(uint8_t cnt);

// 串口控制版本函数
uint8_t vInterpolation_Point_Serial(int16_t POINT_X, int16_t POINT_Y);
void vMotor_Serial_Init(void);

// 根据控制模式选择函数签名
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
void vOne_Step_X(void);  // 脉冲控制模式：无参数
void vOne_Step_Y(void);  // 脉冲控制模式：无参数
#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
void vOne_Step_X(uint8_t dir);  // 串口控制模式：需要方向参数
void vOne_Step_Y(uint8_t dir);  // 串口控制模式：需要方向参数
#endif

// Sine wave functions
void vGenerate_Sine_Wave(int16_t amplitude, int16_t period, uint8_t points, int16_t start_x);
void vDraw_Sine_Wave(int16_t amplitude, int16_t period, uint8_t cycles);
void vTest_Sine_Wave(void);

extern PID_Struct_ motor1_PID;
extern PID_Struct_ motor2_PID;
extern int16_t actual_point[30][2];

#endif
