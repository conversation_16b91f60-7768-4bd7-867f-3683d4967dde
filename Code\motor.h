#ifndef __MOTOR_H
#define __MOTOR_H

#include "stm32f10x.h"
#include "Emm_V5.h"

#define Limit_Out(input,max,min) \
	((input)>(max)?(max):((input)<(min)?(min):(input)))

typedef struct _PID_Struct_
{
	float error[3];
	float kp;
	float ki;
	float kd;
	float pout;
	float iout;
	float dout;
	float out[2];
}PID_Struct_;

// ?????????
#define MOTOR_CONTROL_MODE_PULSE  0   // ??????
#define MOTOR_CONTROL_MODE_SERIAL 1   // ??????

// ??????????????????????
#define CURRENT_MOTOR_CONTROL_MODE MOTOR_CONTROL_MODE_SERIAL

void vDriverInit(void);
void Delayms(u16 ms);

int16_t PID_Control(PID_Struct_ *_PID_,int16_t error);

void vImage_To_Actual(int16_t Image[][2],uint8_t cnt);
uint8_t vInterpolation_Point(int16_t POINT_X,int16_t POINT_Y);//??????
void vInterpolation_Move(uint8_t cnt);

// ????????
uint8_t vInterpolation_Point_Serial(int16_t POINT_X, int16_t POINT_Y);
void vMotor_Serial_Init(void);

// ????????????
#if (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_PULSE)
void vOne_Step_X(void);  // ??????????
void vOne_Step_Y(void);  // ??????????
#elif (CURRENT_MOTOR_CONTROL_MODE == MOTOR_CONTROL_MODE_SERIAL)
void vOne_Step_X(uint8_t dir);  // ?????????????
void vOne_Step_Y(uint8_t dir);  // ?????????????
#endif

// Sine wave functions
void vGenerate_Sine_Wave(int16_t amplitude, int16_t period, uint8_t points, int16_t start_x);
void vDraw_Sine_Wave(int16_t amplitude, int16_t period, uint8_t cycles);
void vTest_Sine_Wave(void);

extern PID_Struct_ motor1_PID;
extern PID_Struct_ motor2_PID;
extern int16_t actual_point[30][2];

#endif
